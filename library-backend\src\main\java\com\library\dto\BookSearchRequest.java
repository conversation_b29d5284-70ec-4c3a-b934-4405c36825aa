package com.library.dto;

import lombok.Data;
import java.time.LocalDate;
import java.util.List;

@Data
public class BookSearchRequest {
    private String keyword;  // 通用搜索关键词
    private String title;
    private String author;
    private String publisher;
    private String isbn;
    private List<Long> categoryIds;
    private LocalDate publishDateStart;
    private LocalDate publishDateEnd;
    private Integer stockMin;
    private Integer stockMax;
    private Boolean onlyAvailable;  // 只显示可借的
    private String sortBy;  // title, author, publishDate, stock, borrowCount
    private String sortOrder;  // ASC, DESC
    private Integer page = 1;
    private Integer pageSize = 10;
    private Integer offset = 0;  // 计算得出的偏移量
}