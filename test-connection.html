<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>图书管理系统 - 前后端连接测试</h1>
    
    <div class="test-section">
        <h3>1. 后端健康检查</h3>
        <button onclick="testHealth()">测试后端连接</button>
        <div id="health-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 登录接口测试</h3>
        <button onclick="testLogin()">测试登录接口</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 图书列表接口测试</h3>
        <button onclick="testBooks()">测试图书列表</button>
        <div id="books-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/health`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 后端连接成功</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 后端响应异常</h4>
                            <pre>状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 连接失败</h4>
                        <pre>错误: ${error.message}</pre>
                        <p>请检查：</p>
                        <ul>
                            <li>后端服务是否启动 (端口8080)</li>
                            <li>数据库是否连接正常</li>
                            <li>防火墙设置</li>
                        </ul>
                    </div>
                `;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 登录接口正常</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 登录失败</h4>
                            <pre>状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 登录请求失败</h4>
                        <pre>错误: ${error.message}</pre>
                    </div>
                `;
            }
        }
        
        async function testBooks() {
            const resultDiv = document.getElementById('books-result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/books`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 图书列表接口正常</h4>
                            <p>共获取到 ${data.data ? data.data.length : 0} 本图书</p>
                            <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 图书列表获取失败</h4>
                            <pre>状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 图书列表请求失败</h4>
                        <pre>错误: ${error.message}</pre>
                    </div>
                `;
            }
        }
        
        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
