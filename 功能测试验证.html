<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书管理系统 - 功能修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-success {
            background-color: #28a745;
            color: white;
        }
        .status-error {
            background-color: #dc3545;
            color: white;
        }
        .status-pending {
            background-color: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 图书管理系统功能修复验证</h1>
        <p>验证所有修复的功能是否正常工作</p>
    </div>
    
    <div class="test-section">
        <h3>🔐 1. 管理员登录测试</h3>
        <button onclick="testAdminLogin()">测试管理员登录</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📊 2. 导出Excel功能测试</h3>
        <button onclick="testExportBooks()">导出图书列表</button>
        <button onclick="testExportBorrowRecords()">导出借阅记录</button>
        <div id="export-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📚 3. 管理员借阅功能测试</h3>
        <button onclick="testAdminBorrow()">管理员借阅图书</button>
        <button onclick="testAdminReserve()">管理员预约图书</button>
        <div id="borrow-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📈 4. 统计数据测试</h3>
        <button onclick="testStatistics()">获取综合统计</button>
        <button onclick="testMonthlyTrend()">月度借阅趋势</button>
        <button onclick="testCategoryStats()">分类借阅统计</button>
        <div id="stats-result"></div>
    </div>
    
    <div class="test-section">
        <h3>🔍 5. 搜索功能测试</h3>
        <button onclick="testSearch()">测试搜索功能</button>
        <div id="search-result"></div>
    </div>
    
    <div class="test-section">
        <h3>📋 6. 系统状态总览</h3>
        <button onclick="runAllTests()">运行所有测试</button>
        <div id="overall-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = '';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const badge = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            element.innerHTML = `<div class="result ${type}">${badge} ${message}</div>`;
        }
        
        function showJsonResult(elementId, data, type = 'success') {
            const element = document.getElementById(elementId);
            const badge = type === 'success' ? '✅' : '❌';
            element.innerHTML = `<div class="result ${type}">${badge} <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
        }
        
        async function makeRequest(url, options = {}) {
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    ...options.headers
                };
                
                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                }
                
                const response = await fetch(url, {
                    headers,
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function testAdminLogin() {
            showResult('login-result', '正在测试管理员登录...', 'info');
            
            const result = await makeRequest(`${API_BASE}/auth/login`, {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });
            
            if (result.success && result.data.data) {
                authToken = result.data.data.token;
                showResult('login-result', `登录成功！Token: ${authToken.substring(0, 20)}...`, 'success');
            } else {
                showResult('login-result', `登录失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testExportBooks() {
            if (!authToken) {
                showResult('export-result', '请先登录！', 'warning');
                return;
            }
            
            showResult('export-result', '正在测试图书导出...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/export/books`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    showResult('export-result', `图书导出成功！文件大小: ${blob.size} bytes`, 'success');
                } else {
                    showResult('export-result', `图书导出失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('export-result', `图书导出失败: ${error.message}`, 'error');
            }
        }
        
        async function testExportBorrowRecords() {
            if (!authToken) {
                showResult('export-result', '请先登录！', 'warning');
                return;
            }
            
            showResult('export-result', '正在测试借阅记录导出...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/export/borrow-records`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    showResult('export-result', `借阅记录导出成功！文件大小: ${blob.size} bytes`, 'success');
                } else {
                    showResult('export-result', `借阅记录导出失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('export-result', `借阅记录导出失败: ${error.message}`, 'error');
            }
        }
        
        async function testAdminBorrow() {
            if (!authToken) {
                showResult('borrow-result', '请先登录！', 'warning');
                return;
            }
            
            showResult('borrow-result', '正在测试管理员借阅功能...', 'info');
            
            const result = await makeRequest(`${API_BASE}/borrow/borrow`, {
                method: 'POST',
                body: JSON.stringify({ bookId: 2 })
            });
            
            if (result.success) {
                showResult('borrow-result', '管理员借阅功能正常！', 'success');
            } else {
                showResult('borrow-result', `借阅失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testAdminReserve() {
            if (!authToken) {
                showResult('borrow-result', '请先登录！', 'warning');
                return;
            }
            
            showResult('borrow-result', '正在测试管理员预约功能...', 'info');
            
            const result = await makeRequest(`${API_BASE}/reservation/create`, {
                method: 'POST',
                body: JSON.stringify({ bookId: 12 })
            });
            
            if (result.success) {
                showResult('borrow-result', '管理员预约功能正常！', 'success');
            } else {
                showResult('borrow-result', `预约测试: ${result.data?.message || '该图书有库存，可直接借阅'}`, 'info');
            }
        }
        
        async function testStatistics() {
            if (!authToken) {
                showResult('stats-result', '请先登录！', 'warning');
                return;
            }
            
            showResult('stats-result', '正在获取综合统计数据...', 'info');
            
            const result = await makeRequest(`${API_BASE}/statistics/comprehensive`);
            
            if (result.success) {
                const data = result.data.data;
                showJsonResult('stats-result', {
                    overview: data.overview,
                    monthlyTrend: data.monthlyTrend,
                    categoryStats: data.categoryStats,
                    topBooks: data.topBooks
                }, 'success');
            } else {
                showResult('stats-result', `统计数据获取失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testMonthlyTrend() {
            if (!authToken) {
                showResult('stats-result', '请先登录！', 'warning');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/statistics/comprehensive`);
            
            if (result.success && result.data.data.monthlyTrend) {
                const trend = result.data.data.monthlyTrend;
                showResult('stats-result', `月度趋势数据: ${trend.length} 个月的数据`, 'success');
            } else {
                showResult('stats-result', '月度趋势数据为空', 'warning');
            }
        }
        
        async function testCategoryStats() {
            if (!authToken) {
                showResult('stats-result', '请先登录！', 'warning');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/statistics/comprehensive`);
            
            if (result.success && result.data.data.categoryStats) {
                const stats = result.data.data.categoryStats;
                showResult('stats-result', `分类统计数据: ${stats.length} 个分类有借阅记录`, 'success');
            } else {
                showResult('stats-result', '分类统计数据为空', 'warning');
            }
        }
        
        async function testSearch() {
            showResult('search-result', '正在测试搜索功能...', 'info');
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify({
                    keyword: 'Java',
                    page: 1,
                    pageSize: 5
                })
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('search-result', `搜索功能正常！找到 ${data.total} 条结果`, 'success');
            } else {
                showResult('search-result', `搜索失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            showResult('overall-result', '正在运行所有测试...', 'info');
            
            const tests = [
                { name: '管理员登录', func: testAdminLogin },
                { name: '图书导出', func: testExportBooks },
                { name: '借阅记录导出', func: testExportBorrowRecords },
                { name: '管理员借阅', func: testAdminBorrow },
                { name: '统计数据', func: testStatistics },
                { name: '搜索功能', func: testSearch }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    await test.func();
                    results.push(`✅ ${test.name}: 通过`);
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
            
            showResult('overall-result', 
                `测试完成！<br><br>${results.join('<br>')}`, 
                'info');
        }
        
        // 页面加载时自动登录
        window.onload = function() {
            testAdminLogin();
        };
    </script>
</body>
</html>
