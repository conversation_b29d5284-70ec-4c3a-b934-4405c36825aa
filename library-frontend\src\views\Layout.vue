<template>
  <el-container class="layout-container">
    <el-aside width="200px" class="sidebar">
      <div class="logo">
        <el-icon><Reading /></el-icon>
        <span>图书管理系统</span>
      </div>
      <el-menu
        :default-active="activeMenu"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <el-menu-item index="/dashboard">
          <el-icon><DataAnalysis /></el-icon>
          <span>系统概览</span>
        </el-menu-item>
        <el-menu-item index="/books">
          <el-icon><Notebook /></el-icon>
          <span>图书管理</span>
        </el-menu-item>
        <el-menu-item index="/borrow">
          <el-icon><Reading /></el-icon>
          <span>借阅记录</span>
        </el-menu-item>
        <el-menu-item index="/reservations">
          <el-icon><Calendar /></el-icon>
          <span>我的预约</span>
        </el-menu-item>
        <el-menu-item index="/categories">
          <el-icon><CollectionTag /></el-icon>
          <span>分类管理</span>
        </el-menu-item>
        <el-menu-item index="/users" v-if="isAdmin">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </el-menu-item>
        <el-menu-item index="/statistics" v-if="isAdmin">
          <el-icon><DataLine /></el-icon>
          <span>统计报表</span>
        </el-menu-item>
        <el-menu-item index="/config" v-if="isAdmin">
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentRouteName">{{ currentRouteName }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0" style="margin-right: 20px">
            <el-button :icon="Bell" circle @click="goToNotifications" />
          </el-badge>
          <el-dropdown>
            <div class="user-info">
              <el-avatar :size="32" style="margin-right: 10px">{{ user?.nickname?.charAt(0) }}</el-avatar>
              <span>{{ user?.nickname }}</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goToChangePassword">修改密码</el-dropdown-item>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <el-main class="main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Reading, DataAnalysis, Notebook, CollectionTag, User, Calendar, DataLine, Setting, Bell } from '@element-plus/icons-vue'
import { notificationApi } from '../api/notification'

const route = useRoute()
const router = useRouter()

const user = ref(JSON.parse(localStorage.getItem('user') || '{}'))
const isAdmin = computed(() => user.value.role === 'ADMIN')
const activeMenu = computed(() => route.path)
const unreadCount = ref(0)

const currentRouteName = computed(() => {
  const nameMap = {
    '/dashboard': '系统概览',
    '/books': '图书管理',
    '/borrow': '借阅记录',
    '/reservations': '我的预约',
    '/categories': '分类管理',
    '/users': '用户管理',
    '/statistics': '统计报表',
    '/config': '系统设置',
    '/notifications': '消息中心',
    '/change-password': '修改密码'
  }
  return nameMap[route.path] || ''
})

const loadUnreadCount = async () => {
  try {
    const res = await notificationApi.getMyNotifications()
    unreadCount.value = res.data.unreadCount || 0
  } catch (error) {
    console.error(error)
  }
}

const goToNotifications = () => {
  router.push('/notifications')
}

const goToChangePassword = () => {
  router.push('/change-password')
}

const handleLogout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  ElMessage.success('已退出登录')
  router.push('/login')
}

onMounted(() => {
  loadUnreadCount()
  // 定期刷新未读消息数
  setInterval(loadUnreadCount, 60000) // 每分钟刷新一次
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo .el-icon {
  margin-right: 10px;
  font-size: 24px;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.main {
  background-color: #f5f7fa;
}
</style>