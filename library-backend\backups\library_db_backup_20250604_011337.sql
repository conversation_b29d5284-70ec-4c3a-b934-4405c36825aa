-- MySQL dump 10.13  Distrib 5.7.19, for Win64 (x86_64)
--
-- Host: localhost    Database: library_db
-- ------------------------------------------------------
-- Server version	5.7.19

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `library_db`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `library_db` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `library_db`;

--
-- Table structure for table `book`
--

DROP TABLE IF EXISTS `book`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `book` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `isbn` varchar(20) NOT NULL COMMENT 'ISBN编号',
  `title` varchar(100) NOT NULL COMMENT '书名',
  `author` varchar(50) NOT NULL COMMENT '作者',
  `publisher` varchar(50) DEFAULT NULL COMMENT '出版社',
  `publish_date` date DEFAULT NULL COMMENT '出版日期',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '封面URL',
  `description` text COMMENT '图书简介',
  `stock` int(11) DEFAULT '0' COMMENT '库存数量',
  `available_stock` int(11) DEFAULT '0' COMMENT '可借数量',
  `borrowed_count` int(11) DEFAULT '0' COMMENT '借出数量',
  `total_borrow_count` int(11) DEFAULT '0' COMMENT '总借阅次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `isbn` (`isbn`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `book`
--

LOCK TABLES `book` WRITE;
/*!40000 ALTER TABLE `book` DISABLE KEYS */;
INSERT INTO `book` VALUES (1,'9787111544937','深入理解Java虚拟机','周志明','机械工业出版社','2019-12-01','https://cover.com/jvm.jpg','Java虚拟机原理深度解析',15,15,0,2,'2025-05-31 18:06:17','2025-06-04 00:51:07'),(2,'9787544281097','三体全集','刘慈欣','重庆出版社','2016-06-01','https://cover.com/three-body.jpg','中国科幻里程碑作品',22,22,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(3,'9787108063106','人类简史','尤瓦尔·赫拉利','中信出版社','2017-02-01','https://cover.com/sapiens.jpg','从动物到上帝的人类发展史',18,17,1,1,'2025-05-31 18:06:17','2025-06-04 00:38:58'),(4,'9787550289374','算法图解','Aditya Bhargava','人民邮电出版社','2020-05-01','https://cover.com/algo.jpg','像小说一样有趣的算法入门书',12,12,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(5,'9787508684031','经济学原理','曼昆','中信出版社','2018-03-15','https://cover.com/economics.jpg','经典经济学教材',9,9,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(6,'9787550254075','活着','余华','作家出版社','2017-08-01','https://cover.com/alive.jpg','一部关于生存的经典小说',25,25,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(7,'9787513348365','Spring Boot实战','Craig Walls','人民邮电出版社','2021-04-01','https://cover.com/springboot.jpg','Spring Boot开发权威指南',8,8,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(8,'9787559633179','Vue.js设计与实现','霍春阳','电子工业出版社','2022-01-01','https://cover.com/vue.jpg','深入解析Vue.js框架原理',11,11,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(9,'9787505742478','时间简史','史蒂芬·霍金','湖南科技出版社','2019-05-01','https://cover.com/time.jpg','探索宇宙奥秘的科普经典',7,7,0,0,'2025-05-31 18:06:17','2025-05-31 18:06:17'),(10,'9787547737995','百年孤独','马尔克斯','南海出版公司','2017-06-01','https://cover.com/solitude.jpg','魔幻现实主义文学代表作',20,20,0,1,'2025-05-31 18:06:17','2025-06-04 01:11:36'),(12,'1111','111111111111','111111111111111','11111111','2025-06-04','111','11111',4,NULL,NULL,NULL,'2025-06-03 23:28:18','2025-06-04 00:50:36'),(13,'22','22','22','22','2025-06-04','22','22',0,0,0,0,'2025-06-04 00:52:19','2025-06-04 00:52:19');
/*!40000 ALTER TABLE `book` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `book_category`
--

DROP TABLE IF EXISTS `book_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `book_category` (
  `book_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  PRIMARY KEY (`book_id`,`category_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `book_category_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `book` (`id`) ON DELETE CASCADE,
  CONSTRAINT `book_category_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `book_category`
--

LOCK TABLES `book_category` WRITE;
/*!40000 ALTER TABLE `book_category` DISABLE KEYS */;
INSERT INTO `book_category` VALUES (1,1),(4,1),(7,1),(8,1),(2,2),(6,2),(10,2),(3,3),(5,4),(13,4),(2,6),(9,6),(12,7);
/*!40000 ALTER TABLE `book_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `borrow_record`
--

DROP TABLE IF EXISTS `borrow_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `borrow_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '借阅用户ID',
  `book_id` int(11) NOT NULL COMMENT '图书ID',
  `borrow_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '借阅时间',
  `due_date` datetime NOT NULL COMMENT '应还时间',
  `return_date` datetime DEFAULT NULL COMMENT '实际归还时间',
  `status` enum('BORROWED','RETURNED','OVERDUE') DEFAULT 'BORROWED' COMMENT '借阅状态',
  `renew_count` int(11) DEFAULT '0' COMMENT '续借次数',
  PRIMARY KEY (`id`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_book_status` (`book_id`,`status`),
  CONSTRAINT `borrow_record_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `borrow_record_ibfk_2` FOREIGN KEY (`book_id`) REFERENCES `book` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `borrow_record`
--

LOCK TABLES `borrow_record` WRITE;
/*!40000 ALTER TABLE `borrow_record` DISABLE KEYS */;
INSERT INTO `borrow_record` VALUES (1,4,1,'2025-06-04 00:38:11','2025-07-21 00:38:11','2025-06-04 00:38:27','RETURNED',1),(2,4,3,'2025-06-04 00:38:58','2025-07-05 00:38:58',NULL,'BORROWED',0),(3,1,1,'2025-06-04 00:51:01','2025-07-04 00:51:02','2025-06-04 00:51:08','RETURNED',0),(4,1,10,'2025-06-04 01:10:10','2025-07-20 01:10:10','2025-06-04 01:11:37','RETURNED',1);
/*!40000 ALTER TABLE `borrow_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `category`
--

DROP TABLE IF EXISTS `category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `category`
--

LOCK TABLES `category` WRITE;
/*!40000 ALTER TABLE `category` DISABLE KEYS */;
INSERT INTO `category` VALUES (9,'医学健康'),(3,'历史传记'),(7,'心理学'),(8,'教育学习'),(2,'文学小说'),(6,'科普读物'),(4,'经济管理'),(5,'艺术设计'),(1,'计算机科学');
/*!40000 ALTER TABLE `category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification`
--

DROP TABLE IF EXISTS `notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '接收用户ID',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `type` enum('SYSTEM','BORROW','RETURN','OVERDUE','RESERVATION') DEFAULT 'SYSTEM' COMMENT '通知类型',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_read` (`user_id`,`is_read`),
  CONSTRAINT `notification_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification`
--

LOCK TABLES `notification` WRITE;
/*!40000 ALTER TABLE `notification` DISABLE KEYS */;
INSERT INTO `notification` VALUES (1,4,'借阅成功','您已成功借阅《深入理解Java虚拟机》，请在31天内归还','BORROW',1,'2025-06-04 00:38:11'),(2,4,'续借成功','《深入理解Java虚拟机》续借成功，已延长16天','BORROW',1,'2025-06-04 00:38:24'),(3,4,'归还成功','您已成功归还《深入理解Java虚拟机》','RETURN',1,'2025-06-04 00:38:26'),(4,4,'预约成功','您已成功预约《111111111111》，当前排队位置：1','RESERVATION',1,'2025-06-04 00:38:36'),(5,4,'借阅成功','您已成功借阅《人类简史》，请在31天内归还','BORROW',1,'2025-06-04 00:38:58'),(6,1,'借阅成功','您已成功借阅《深入理解Java虚拟机》，请在30天内归还','BORROW',1,'2025-06-04 00:51:01'),(7,1,'归还成功','您已成功归还《深入理解Java虚拟机》','RETURN',1,'2025-06-04 00:51:07'),(8,1,'预约成功','您已成功预约《22》，当前排队位置：1','RESERVATION',1,'2025-06-04 00:52:24'),(9,1,'预约已取消','您对《22》的预约已取消','RESERVATION',1,'2025-06-04 00:52:36'),(10,1,'借阅成功','您已成功借阅《百年孤独》，请在30天内归还','BORROW',1,'2025-06-04 01:10:10'),(11,1,'续借成功','《百年孤独》续借成功，已延长16天','BORROW',1,'2025-06-04 01:11:33'),(12,1,'归还成功','您已成功归还《百年孤独》','RETURN',1,'2025-06-04 01:11:36');
/*!40000 ALTER TABLE `notification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_log`
--

DROP TABLE IF EXISTS `operation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `target` varchar(50) DEFAULT NULL COMMENT '操作对象',
  `target_id` int(11) DEFAULT NULL COMMENT '操作对象ID',
  `detail` text COMMENT '操作详情',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_operation` (`operation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_log`
--

LOCK TABLES `operation_log` WRITE;
/*!40000 ALTER TABLE `operation_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `operation_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reservation`
--

DROP TABLE IF EXISTS `reservation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reservation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '预约用户ID',
  `book_id` int(11) NOT NULL COMMENT '图书ID',
  `reservation_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  `status` enum('WAITING','NOTIFIED','CANCELLED','FULFILLED') DEFAULT 'WAITING' COMMENT '预约状态',
  `notify_date` datetime DEFAULT NULL COMMENT '通知时间',
  `expire_date` datetime DEFAULT NULL COMMENT '预约过期时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_book_status` (`book_id`,`status`),
  CONSTRAINT `reservation_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `reservation_ibfk_2` FOREIGN KEY (`book_id`) REFERENCES `book` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reservation`
--

LOCK TABLES `reservation` WRITE;
/*!40000 ALTER TABLE `reservation` DISABLE KEYS */;
INSERT INTO `reservation` VALUES (1,4,12,'2025-06-04 00:38:36','WAITING',NULL,NULL),(2,1,13,'2025-06-04 00:52:24','CANCELLED',NULL,NULL);
/*!40000 ALTER TABLE `reservation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '登录账号',
  `password` varchar(100) NOT NULL COMMENT '加密密码',
  `nickname` varchar(50) NOT NULL COMMENT '用户昵称',
  `role` enum('ADMIN','USER') DEFAULT 'USER' COMMENT '用户角色',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `max_borrow_count` int(11) DEFAULT '5' COMMENT '最大借阅数量',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,'admin','$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi','图书管理员','ADMIN',NULL,NULL,10,1,'2025-05-31 18:06:17'),(3,'mike','$2a$10$7BPfcvTpGST1/KzJ.8.VReB4JgHPSu9Y4Q1cj9V8L1e1I3G6F3VgG','读者服务','USER',NULL,NULL,5,1,'2025-05-31 18:06:17'),(4,'test00','$2a$10$dSyi1nT1OWWy4g1s4F7S9uajR75R8vpwzVQ03pOPgsy4nHY7LIcDy','liming','USER',NULL,NULL,5,1,'2025-06-04 00:37:48');
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_config`
--

DROP TABLE IF EXISTS `system_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` varchar(200) NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置说明',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_config`
--

LOCK TABLES `system_config` WRITE;
/*!40000 ALTER TABLE `system_config` DISABLE KEYS */;
INSERT INTO `system_config` VALUES (1,'borrow_days','30','默认借阅天数','2025-05-31 18:06:17','2025-06-04 00:46:52'),(2,'max_borrow_count','6','最大同时借阅数量','2025-05-31 18:06:17','2025-06-03 23:28:50'),(3,'max_renew_count','3','最大续借次数','2025-05-31 18:06:17','2025-06-03 23:28:49'),(4,'renew_days','16','每次续借天数','2025-05-31 18:06:17','2025-06-03 23:28:49'),(5,'overdue_fine_per_day','1','每天逾期罚金(元)','2025-05-31 18:06:17','2025-06-03 23:28:56'),(6,'reservation_expire_days','4','预约保留天数','2025-05-31 18:06:17','2025-06-03 23:28:47'),(7,'allow_reservation','true','是否允许预约','2025-05-31 18:06:17','2025-05-31 18:06:17'),(8,'allow_renew','true','是否允许续借','2025-05-31 18:06:17','2025-05-31 18:06:17');
/*!40000 ALTER TABLE `system_config` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-04  1:13:37
