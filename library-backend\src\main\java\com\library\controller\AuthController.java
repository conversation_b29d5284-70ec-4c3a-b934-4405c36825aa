package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.LoginRequest;
import com.library.entity.User;
import com.library.service.UserService;
import com.library.utils.JwtUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtils jwtUtils;

    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("服务正常运行", "OK");
    }

    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@Valid @RequestBody LoginRequest request) {
        User user = userService.login(request.getUsername(), request.getPassword());
        
        if (user == null) {
            return ApiResponse.error("用户名或密码错误");
        }
        
        String token = jwtUtils.generateToken(user.getId(), user.getUsername(), user.getRole());
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        
        return ApiResponse.success("登录成功", result);
    }

    @PostMapping("/register")
    public ApiResponse<String> register(@Valid @RequestBody User user) {
        // 设置默认角色为普通用户
        if (user.getRole() == null || user.getRole().isEmpty()) {
            user.setRole("USER");
        }

        if (userService.create(user)) {
            return ApiResponse.success("注册成功", null);
        }
        return ApiResponse.error("用户名已存在");
    }
}