package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.User;
import com.library.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import java.util.List;

@RestController
@RequestMapping("/api/users")
@CrossOrigin
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public ApiResponse<List<User>> getAllUsers(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        List<User> users = userService.findAll();
        // 隐藏密码
        users.forEach(user -> user.setPassword(null));
        return ApiResponse.success(users);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<User> getUserById(@PathVariable Long id) {
        User user = userService.findById(id);
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }
        user.setPassword(null);
        return ApiResponse.success(user);
    }
    
    @PostMapping
    public ApiResponse<String> createUser(@Valid @RequestBody User user, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        if (userService.create(user)) {
            return ApiResponse.success("创建成功", null);
        }
        return ApiResponse.error("用户名已存在");
    }
    
    @PutMapping("/{id}")
    public ApiResponse<String> updateUser(@PathVariable Long id, @Valid @RequestBody User user, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        user.setId(id);
        if (userService.update(user)) {
            return ApiResponse.success("更新成功", null);
        }
        return ApiResponse.error("更新失败");
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteUser(@PathVariable Long id, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        if (userService.deleteById(id)) {
            return ApiResponse.success("删除成功", null);
        }
        return ApiResponse.error("删除失败");
    }

    @PostMapping("/change-password")
    public ApiResponse<String> changePassword(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        Long userId = (Long) httpRequest.getAttribute("userId");
        String currentPassword = (String) request.get("currentPassword");
        String newPassword = (String) request.get("newPassword");

        if (userService.changePassword(userId, currentPassword, newPassword)) {
            return ApiResponse.success("密码修改成功", null);
        }
        return ApiResponse.error("当前密码错误");
    }
}