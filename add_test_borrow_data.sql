-- 添加测试借阅数据，用于展示月度借阅趋势和分类借阅统计

-- 插入一些测试借阅记录（最近几个月的数据）
INSERT INTO borrow_record (user_id, book_id, borrow_date, due_date, status, create_time) VALUES
-- 2024年4月的借阅记录
(1, 1, '2024-04-15 10:00:00', '2024-05-16 10:00:00', 'RETURNED', '2024-04-15 10:00:00'),
(3, 2, '2024-04-18 14:30:00', '2024-05-19 14:30:00', 'RETURNED', '2024-04-18 14:30:00'),
(1, 4, '2024-04-22 09:15:00', '2024-05-23 09:15:00', 'RETURNED', '2024-04-22 09:15:00'),

-- 2024年5月的借阅记录
(3, 3, '2024-05-05 11:20:00', '2024-06-05 11:20:00', 'RETURNED', '2024-05-05 11:20:00'),
(1, 7, '2024-05-12 16:45:00', '2024-06-12 16:45:00', 'RETURNED', '2024-05-12 16:45:00'),
(3, 8, '2024-05-18 13:30:00', '2024-06-18 13:30:00', 'RETURNED', '2024-05-18 13:30:00'),
(1, 5, '2024-05-25 10:10:00', '2024-06-25 10:10:00', 'RETURNED', '2024-05-25 10:10:00'),

-- 2024年6月的借阅记录（当前月）
(3, 6, '2024-06-02 09:30:00', '2024-07-03 09:30:00', 'BORROWED', '2024-06-02 09:30:00'),
(1, 9, '2024-06-03 14:20:00', '2024-07-04 14:20:00', 'BORROWED', '2024-06-03 14:20:00'),
(3, 10, '2024-06-03 15:45:00', '2024-07-04 15:45:00', 'BORROWED', '2024-06-03 15:45:00');

-- 更新图书的借阅统计数据
UPDATE book SET 
    borrowed_count = (SELECT COUNT(*) FROM borrow_record WHERE book_id = book.id AND status = 'BORROWED'),
    total_borrow_count = (SELECT COUNT(*) FROM borrow_record WHERE book_id = book.id),
    available_stock = stock - (SELECT COUNT(*) FROM borrow_record WHERE book_id = book.id AND status = 'BORROWED')
WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10);

-- 验证数据
SELECT 
    DATE_FORMAT(borrow_date, '%Y-%m') as month, 
    COUNT(*) as count 
FROM borrow_record 
WHERE borrow_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
GROUP BY DATE_FORMAT(borrow_date, '%Y-%m') 
ORDER BY month;

-- 验证分类借阅统计
SELECT 
    c.name as category, 
    COUNT(br.id) as borrowCount 
FROM category c 
JOIN book_category bc ON c.id = bc.category_id 
JOIN book b ON bc.book_id = b.id 
JOIN borrow_record br ON b.id = br.book_id 
WHERE br.borrow_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
GROUP BY c.id, c.name 
ORDER BY borrowCount DESC;
