<template>
  <div class="books-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>图书管理</span>
          <el-button type="primary" @click="handleAdd">添加图书</el-button>
        </div>
      </template>
      
      <el-form :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input v-model="searchForm.keyword" placeholder="搜索书名、作者、出版社等" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.categoryIds" multiple placeholder="选择分类" clearable style="width: 200px">
            <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="只看可借">
          <el-switch v-model="searchForm.onlyAvailable" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="showAdvancedSearch = !showAdvancedSearch">
            {{ showAdvancedSearch ? '简单搜索' : '高级搜索' }}
          </el-button>
          <el-button @click="handleExport" v-if="isAdmin">导出Excel</el-button>
          <el-upload
            v-if="isAdmin"
            class="upload-demo"
            :show-file-list="false"
            :before-upload="handleImport"
            style="display: inline-block; margin-left: 10px"
          >
            <el-button>导入Excel</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <!-- 高级搜索 -->
      <el-collapse-transition>
        <el-form v-show="showAdvancedSearch" :inline="true" class="search-form" style="margin-top: 10px">
          <el-form-item label="ISBN">
            <el-input v-model="searchForm.isbn" placeholder="请输入ISBN" clearable />
          </el-form-item>
          <el-form-item label="出版社">
            <el-input v-model="searchForm.publisher" placeholder="请输入出版社" clearable />
          </el-form-item>
          <el-form-item label="库存范围">
            <el-input-number v-model="searchForm.stockMin" :min="0" placeholder="最小" style="width: 100px" />
            <span style="margin: 0 5px">-</span>
            <el-input-number v-model="searchForm.stockMax" :min="0" placeholder="最大" style="width: 100px" />
          </el-form-item>
          <el-form-item label="排序">
            <el-select v-model="searchForm.sortBy" placeholder="选择排序" clearable>
              <el-option label="按书名" value="title" />
              <el-option label="按作者" value="author" />
              <el-option label="按出版日期" value="publishDate" />
              <el-option label="按库存" value="stock" />
              <el-option label="按借阅次数" value="borrowCount" />
            </el-select>
            <el-radio-group v-model="searchForm.sortOrder" style="margin-left: 10px">
              <el-radio label="ASC">升序</el-radio>
              <el-radio label="DESC">降序</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-collapse-transition>
      
      <el-table :data="filteredBooks" style="width: 100%" v-loading="loading">
        <el-table-column prop="isbn" label="ISBN" width="150" />
        <el-table-column prop="title" label="书名" />
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="publisher" label="出版社" width="150" />
        <el-table-column prop="publishDate" label="出版日期" width="120" />
        <el-table-column label="库存" width="150">
          <template #default="scope">
            <div>总库存: {{ scope.row.stock }}</div>
            <div>可借: <el-tag :type="getStockType(scope.row.availableStock)">{{ scope.row.availableStock || 0 }}</el-tag></div>
          </template>
        </el-table-column>
        <el-table-column prop="categories" label="分类" width="200">
          <template #default="scope">
            <el-tag v-for="category in scope.row.categories" :key="category.id" style="margin-right: 5px">
              {{ category.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button v-if="!isAdmin" size="small" type="primary" @click="handleBorrow(scope.row)" 
                      :disabled="scope.row.availableStock <= 0">借阅</el-button>
            <el-button v-if="!isAdmin && scope.row.availableStock <= 0" size="small" type="warning" 
                      @click="handleReserve(scope.row)">预约</el-button>
            <el-button v-if="isAdmin" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-if="isAdmin" size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div v-if="isSearchMode && total > 0" style="margin-top: 20px; text-align: center">
        <el-pagination
          v-model:current-page="searchForm.page"
          v-model:page-size="searchForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="ISBN" prop="isbn">
          <el-input v-model="form.isbn" placeholder="请输入ISBN" />
        </el-form-item>
        <el-form-item label="书名" prop="title">
          <el-input v-model="form.title" placeholder="请输入书名" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="出版社" prop="publisher">
          <el-input v-model="form.publisher" placeholder="请输入出版社" />
        </el-form-item>
        <el-form-item label="出版日期" prop="publishDate">
          <el-date-picker v-model="form.publishDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="封面URL" prop="coverUrl">
          <el-input v-model="form.coverUrl" placeholder="请输入封面URL" />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number v-model="form.stock" :min="0" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryIds">
          <el-select v-model="form.categoryIds" multiple placeholder="请选择分类" style="width: 100%">
            <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入图书简介" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { bookApi } from '../api/book'
import { categoryApi } from '../api/category'
import { borrowApi } from '../api/borrow'
import { reservationApi } from '../api/reservation'

const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const books = ref([])
const categories = ref([])
const formRef = ref()
const showAdvancedSearch = ref(false)
const total = ref(0)
const isSearchMode = ref(false)

const user = JSON.parse(localStorage.getItem('user') || '{}')
const isAdmin = computed(() => user.role === 'ADMIN')

const searchForm = reactive({
  keyword: '',
  title: '',
  author: '',
  publisher: '',
  isbn: '',
  categoryIds: [],
  stockMin: null,
  stockMax: null,
  onlyAvailable: false,
  sortBy: '',
  sortOrder: 'ASC',
  page: 1,
  pageSize: 10
})

const form = reactive({
  id: null,
  isbn: '',
  title: '',
  author: '',
  publisher: '',
  publishDate: '',
  coverUrl: '',
  description: '',
  stock: 0,
  categoryIds: []
})

const rules = {
  isbn: [{ required: true, message: '请输入ISBN', trigger: 'blur' }],
  title: [{ required: true, message: '请输入书名', trigger: 'blur' }],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入库存', trigger: 'blur' }]
}

const filteredBooks = computed(() => {
  if (showAdvancedSearch.value || searchForm.keyword || searchForm.categoryIds.length > 0 || searchForm.onlyAvailable) {
    // 使用搜索结果
    return books.value
  }
  return books.value.filter(book => {
    return (searchForm.title === '' || book.title.includes(searchForm.title)) && 
           (searchForm.author === '' || book.author.includes(searchForm.author))
  })
})

const getStockType = (stock) => {
  if (!stock || stock === 0) return 'danger'
  if (stock > 15) return 'success'
  if (stock > 5) return 'warning'
  return 'danger'
}

const loadBooks = async () => {
  loading.value = true
  try {
    const res = await bookApi.getAll()
    books.value = res.data || []
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const res = await categoryApi.getAll()
    categories.value = res.data || []
  } catch (error) {
    console.error(error)
  }
}

const handleSearch = async () => {
  if (showAdvancedSearch.value || searchForm.keyword || searchForm.categoryIds.length > 0 || searchForm.onlyAvailable) {
    // 使用高级搜索API
    loading.value = true
    isSearchMode.value = true
    try {
      console.log('搜索参数:', searchForm)
      const res = await bookApi.search(searchForm)
      console.log('搜索结果:', res)
      books.value = res.data.books || []
      total.value = res.data.total || 0
      ElMessage.success(`找到 ${total.value} 条结果`)
    } catch (error) {
      console.error('搜索失败:', error)
      ElMessage.error('搜索失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      books.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  } else {
    // 使用本地过滤
    isSearchMode.value = false
    loadBooks()
  }
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'page') searchForm[key] = 1
    else if (key === 'pageSize') searchForm[key] = 10
    else if (key === 'sortOrder') searchForm[key] = 'ASC'
    else if (key === 'categoryIds') searchForm[key] = []
    else if (key === 'onlyAvailable') searchForm[key] = false
    else if (key === 'stockMin' || key === 'stockMax') searchForm[key] = null
    else searchForm[key] = ''
  })
  showAdvancedSearch.value = false
  isSearchMode.value = false
  total.value = 0
  loadBooks()
}

const handleAdd = () => {
  dialogTitle.value = '添加图书'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑图书'
  Object.assign(form, row)
  form.categoryIds = row.categories?.map(c => c.id) || []
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该图书吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await bookApi.delete(row.id)
    ElMessage.success('删除成功')
    loadBooks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleSubmit = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  
  try {
    if (form.id) {
      await bookApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await bookApi.create(form)
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    loadBooks()
  } catch (error) {
    console.error(error)
  }
}

const resetForm = () => {
  form.id = null
  form.isbn = ''
  form.title = ''
  form.author = ''
  form.publisher = ''
  form.publishDate = ''
  form.coverUrl = ''
  form.description = ''
  form.stock = 0
  form.categoryIds = []
}

const handleBorrow = async (book) => {
  try {
    await ElMessageBox.confirm(`确定要借阅《${book.title}》吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    await borrowApi.borrowBook(book.id)
    ElMessage.success('借阅成功')
    loadBooks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleReserve = async (book) => {
  try {
    await ElMessageBox.confirm(`确定要预约《${book.title}》吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    await reservationApi.createReservation(book.id)
    ElMessage.success('预约成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleExport = async () => {
  try {
    const response = await bookApi.exportBooks()
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `图书列表_${new Date().getTime()}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    ElMessage.success('导出成功')
  } catch (error) {
    console.error(error)
  }
}

const handleImport = async (file) => {
  try {
    await bookApi.importBooks(file)
    ElMessage.success('导入成功')
    loadBooks()
    return false // 阻止默认上传
  } catch (error) {
    console.error(error)
    return false
  }
}

// 分页处理
const handlePageChange = () => {
  if (isSearchMode.value) {
    handleSearch()
  }
}

onMounted(() => {
  loadBooks()
  loadCategories()
})
</script>

<style scoped>
.books-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}
</style>