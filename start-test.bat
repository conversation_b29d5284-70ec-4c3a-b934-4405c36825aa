@echo off
echo ========================================
echo 图书管理系统 - 启动和测试脚本
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请安装Java 17或更高版本
    pause
    exit /b 1
)
echo.

echo 2. 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js环境，请安装Node.js
    pause
    exit /b 1
)
echo.

echo 3. 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: MySQL服务未运行，请确保MySQL已启动
    echo 您可以手动启动MySQL服务
    pause
)
echo.

echo 4. 启动后端服务...
echo 正在启动Spring Boot应用...
cd library-backend
start "Library Backend" cmd /k "mvn spring-boot:run"
echo 后端服务启动中，请等待30秒...
timeout /t 30 /nobreak
cd ..
echo.

echo 5. 启动前端服务...
echo 正在启动Vue.js应用...
cd library-frontend
start "Library Frontend" cmd /k "npm run dev"
echo 前端服务启动中，请等待10秒...
timeout /t 10 /nobreak
cd ..
echo.

echo 6. 打开测试页面...
start test-connection.html
echo.

echo ========================================
echo 启动完成！
echo ========================================
echo 后端地址: http://localhost:8080
echo 前端地址: http://localhost:3000
echo 测试页面: test-connection.html
echo.
echo 如果遇到问题，请检查：
echo 1. MySQL数据库是否运行并导入了library_db.sql
echo 2. 端口8080和3000是否被占用
echo 3. 防火墙是否阻止了连接
echo.
pause
