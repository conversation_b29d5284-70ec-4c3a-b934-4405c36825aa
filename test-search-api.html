<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>图书管理系统 - 搜索功能测试</h1>
    
    <div class="test-section">
        <h3>1. 健康检查</h3>
        <button onclick="testHealth()">测试后端健康状态</button>
        <div id="health-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 获取图书列表</h3>
        <button onclick="testGetBooks()">获取所有图书</button>
        <div id="books-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 搜索功能测试</h3>
        <button onclick="testSearchKeyword()">关键词搜索 (Java)</button>
        <button onclick="testSearchCategory()">分类搜索</button>
        <button onclick="testSearchAvailable()">可借图书搜索</button>
        <button onclick="testSearchAdvanced()">高级搜索</button>
        <div id="search-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 分页测试</h3>
        <button onclick="testPagination()">分页搜索</button>
        <div id="pagination-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function showJsonResult(elementId, data, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}"><pre>${JSON.stringify(data, null, 2)}</pre></div>`;
        }
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function testHealth() {
            showResult('health-result', '正在测试...', 'info');
            const result = await makeRequest(`${API_BASE}/auth/health`);
            
            if (result.success) {
                showJsonResult('health-result', result.data, 'success');
            } else {
                showResult('health-result', `错误: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testGetBooks() {
            showResult('books-result', '正在获取图书列表...', 'info');
            const result = await makeRequest(`${API_BASE}/books`);
            
            if (result.success) {
                const books = result.data.data || [];
                showResult('books-result', `成功获取 ${books.length} 本图书`, 'success');
                console.log('图书列表:', books);
            } else {
                showResult('books-result', `错误: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testSearchKeyword() {
            showResult('search-result', '正在搜索关键词 "Java"...', 'info');
            const searchParams = {
                keyword: 'Java',
                page: 1,
                pageSize: 10
            };
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify(searchParams)
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('search-result', 
                    `搜索成功！找到 ${data.total} 条结果，当前页显示 ${data.books.length} 条`, 
                    'success');
                console.log('搜索结果:', data);
            } else {
                showResult('search-result', `搜索失败: ${result.error || result.data?.message}`, 'error');
                console.error('搜索错误:', result);
            }
        }
        
        async function testSearchCategory() {
            showResult('search-result', '正在搜索计算机科学分类...', 'info');
            const searchParams = {
                categoryIds: [1], // 计算机科学
                page: 1,
                pageSize: 10
            };
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify(searchParams)
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('search-result', 
                    `分类搜索成功！找到 ${data.total} 条结果`, 
                    'success');
                console.log('分类搜索结果:', data);
            } else {
                showResult('search-result', `分类搜索失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testSearchAvailable() {
            showResult('search-result', '正在搜索可借图书...', 'info');
            const searchParams = {
                onlyAvailable: true,
                page: 1,
                pageSize: 10
            };
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify(searchParams)
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('search-result', 
                    `可借图书搜索成功！找到 ${data.total} 条结果`, 
                    'success');
                console.log('可借图书搜索结果:', data);
            } else {
                showResult('search-result', `可借图书搜索失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testSearchAdvanced() {
            showResult('search-result', '正在进行高级搜索...', 'info');
            const searchParams = {
                author: '周志明',
                stockMin: 10,
                sortBy: 'title',
                sortOrder: 'ASC',
                page: 1,
                pageSize: 5
            };
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify(searchParams)
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('search-result', 
                    `高级搜索成功！找到 ${data.total} 条结果`, 
                    'success');
                console.log('高级搜索结果:', data);
            } else {
                showResult('search-result', `高级搜索失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        async function testPagination() {
            showResult('pagination-result', '正在测试分页功能...', 'info');
            const searchParams = {
                keyword: '',
                page: 1,
                pageSize: 5
            };
            
            const result = await makeRequest(`${API_BASE}/books/search`, {
                method: 'POST',
                body: JSON.stringify(searchParams)
            });
            
            if (result.success) {
                const data = result.data.data;
                showResult('pagination-result', 
                    `分页测试成功！总计 ${data.total} 条，共 ${data.totalPages} 页，当前第 ${data.page} 页，每页 ${data.pageSize} 条`, 
                    'success');
                console.log('分页结果:', data);
            } else {
                showResult('pagination-result', `分页测试失败: ${result.error || result.data?.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试健康状态
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
