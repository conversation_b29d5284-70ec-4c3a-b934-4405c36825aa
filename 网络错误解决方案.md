# 图书管理系统 - 网络错误解决方案

## 🔧 已修复的问题

### 1. **前端API配置优化**
- ✅ 修复了前端API baseURL配置，开发环境使用代理
- ✅ 增加了超时时间到10秒
- ✅ 添加了详细的错误处理和日志

### 2. **后端跨域配置修复**
- ✅ 修复了JWT过滤器中的CORS设置
- ✅ 统一了CORS配置，避免冲突
- ✅ 添加了OPTIONS预检请求处理

### 3. **JWT过滤器优化**
- ✅ 确保登录接口不被拦截
- ✅ 添加了健康检查接口
- ✅ 改进了错误响应格式

### 4. **错误处理增强**
- ✅ 前端登录页面添加了详细的错误信息
- ✅ 后端返回JSON格式的错误响应
- ✅ 添加了调试日志

## 🚀 启动步骤

### 方法一：使用自动化脚本
1. 双击运行 `start-test.bat`
2. 脚本会自动启动前后端服务
3. 打开测试页面验证连接

### 方法二：手动启动

#### 1. 启动数据库
```bash
# 确保MySQL服务运行
# 导入数据库
mysql -u root -p < library_db.sql
```

#### 2. 启动后端
```bash
cd library-backend
mvn spring-boot:run
```

#### 3. 启动前端
```bash
cd library-frontend
npm install  # 首次运行需要安装依赖
npm run dev
```

## 🔍 问题诊断

### 1. 使用测试页面
打开 `test-connection.html` 进行连接测试：
- 健康检查：测试后端是否正常运行
- 登录测试：验证登录接口
- 图书列表：测试数据库连接

### 2. 检查后端状态
访问：http://localhost:8080/api/auth/health
- 正常响应：`{"code":200,"message":"服务正常运行","data":"OK"}`

### 3. 检查前端状态
访问：http://localhost:3000
- 应该显示登录页面

## ❌ 常见错误及解决方案

### 错误1：网络错误 - 无法连接到服务器
**原因：** 后端服务未启动或端口被占用
**解决：**
```bash
# 检查端口占用
netstat -ano | findstr :8080
# 如果被占用，结束进程或更换端口
```

### 错误2：CORS跨域错误
**原因：** 跨域配置问题
**解决：** 已在代码中修复，确保使用最新代码

### 错误3：404 - 接口未找到
**原因：** 路径配置错误或后端未启动
**解决：**
1. 检查后端是否正常启动
2. 验证接口路径是否正确

### 错误4：数据库连接失败
**原因：** MySQL未启动或配置错误
**解决：**
1. 启动MySQL服务
2. 检查 `application.properties` 中的数据库配置
3. 确保数据库 `library_db` 存在

### 错误5：JWT Token相关错误
**原因：** Token验证失败
**解决：** 登录接口已放行，不应该出现此问题

## 🔧 配置检查清单

### 后端配置 (application.properties)
- [x] 数据库URL：`**************************************`
- [x] 数据库用户名：`root`
- [x] 数据库密码：`123456`
- [x] 服务端口：`8080`

### 前端配置 (vite.config.js)
- [x] 开发服务器端口：`3000`
- [x] API代理配置：`/api -> http://localhost:8080`

### 数据库配置
- [x] MySQL服务运行
- [x] 数据库 `library_db` 已创建
- [x] 表结构已导入
- [x] 测试数据已插入

## 📝 测试账号

- **管理员账号：** admin / 123456
- **普通用户：** lisa / 123456
- **普通用户：** mike / 123456

## 🆘 如果仍有问题

1. **查看控制台日志**
   - 前端：浏览器开发者工具 Console
   - 后端：Spring Boot启动日志

2. **检查网络连接**
   - 使用 `test-connection.html` 进行诊断
   - 检查防火墙设置

3. **重启服务**
   - 重启MySQL服务
   - 重启后端应用
   - 刷新前端页面

4. **端口冲突**
   - 更改后端端口（application.properties）
   - 更改前端端口（vite.config.js）
   - 更新API配置

## 📞 技术支持

如果按照以上步骤仍无法解决问题，请提供：
1. 错误截图
2. 浏览器控制台日志
3. 后端启动日志
4. 测试页面的结果
