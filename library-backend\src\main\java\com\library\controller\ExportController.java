package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.Book;
import com.library.service.BookService;
import com.library.service.BorrowService;
import com.library.utils.ExcelUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/api/export")
@CrossOrigin
public class ExportController {
    
    @Autowired
    private BookService bookService;
    
    @Autowired
    private BorrowService borrowService;
    
    @Autowired
    private ExcelUtils excelUtils;
    
    @GetMapping("/books")
    public void exportBooks(HttpServletResponse response, HttpServletRequest request) throws IOException {
        // 移除权限检查，因为JWT过滤器已经处理了认证
        // 所有登录用户都可以导出（管理员和普通用户）
        
        List<Book> books = bookService.findAll();
        byte[] excelData = excelUtils.exportBooks(books);
        
        String filename = "图书列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
        response.getOutputStream().write(excelData);
    }
    
    @GetMapping("/borrow-records")
    public void exportBorrowRecords(HttpServletResponse response, HttpServletRequest request) throws IOException {
        // 只有管理员可以导出借阅记录
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            response.setStatus(403);
            return;
        }
        
        var records = borrowService.findAll();
        byte[] excelData = excelUtils.exportBorrowRecords(records);
        
        String filename = "借阅记录_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
        response.getOutputStream().write(excelData);
    }
    
    @PostMapping("/import/books")
    public ApiResponse<String> importBooks(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        if (file.isEmpty()) {
            return ApiResponse.error("文件不能为空");
        }
        
        try {
            List<Book> books = excelUtils.importBooks(file.getInputStream());
            int successCount = 0;
            int failCount = 0;
            
            for (Book book : books) {
                if (bookService.create(book, null)) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
            
            return ApiResponse.success(String.format("导入完成：成功 %d 条，失败 %d 条", successCount, failCount), null);
        } catch (IOException e) {
            return ApiResponse.error("文件读取失败：" + e.getMessage());
        }
    }
}