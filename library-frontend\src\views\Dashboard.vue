<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic title="图书总数" :value="stats.bookCount">
              <template #prefix>
                <el-icon style="vertical-align: middle"><Notebook /></el-icon>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic title="分类数量" :value="stats.categoryCount">
              <template #prefix>
                <el-icon style="vertical-align: middle"><CollectionTag /></el-icon>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic title="用户数量" :value="stats.userCount">
              <template #prefix>
                <el-icon style="vertical-align: middle"><User /></el-icon>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-statistic title="库存预警" :value="stats.lowStockCount">
              <template #prefix>
                <el-icon style="vertical-align: middle" color="#f56c6c"><Warning /></el-icon>
              </template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 统计图表 -->
    <el-row :gutter="20" style="margin-top: 20px" v-if="isAdmin">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>月度借阅趋势</span>
            </div>
          </template>
          <div id="monthlyTrendChart" style="height: 300px;">
            <div v-if="monthlyTrendData.length === 0" style="text-align: center; padding: 100px 0; color: #999;">
              暂无数据
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>分类借阅统计</span>
            </div>
          </template>
          <div id="categoryChart" style="height: 300px;">
            <div v-if="categoryStatsData.length === 0" style="text-align: center; padding: 100px 0; color: #999;">
              暂无数据
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="book-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>最新图书</span>
          <el-button type="primary" @click="goToBooks">查看全部</el-button>
        </div>
      </template>
      <el-table :data="recentBooks" style="width: 100%">
        <el-table-column prop="title" label="书名" />
        <el-table-column prop="author" label="作者" />
        <el-table-column prop="publisher" label="出版社" />
        <el-table-column prop="stock" label="库存">
          <template #default="scope">
            <el-tag :type="getStockType(scope.row.stock)">{{ scope.row.stock }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Notebook, CollectionTag, User, Warning } from '@element-plus/icons-vue'
import { bookApi } from '../api/book'
import { categoryApi } from '../api/category'
import { userApi } from '../api/user'
import { statisticsApi } from '../api/statistics'

const router = useRouter()
const stats = ref({
  bookCount: 0,
  categoryCount: 0,
  userCount: 0,
  lowStockCount: 0
})
const recentBooks = ref([])
const monthlyTrendData = ref([])
const categoryStatsData = ref([])

const user = JSON.parse(localStorage.getItem('user') || '{}')
const isAdmin = computed(() => user.role === 'ADMIN')

const getStockType = (stock) => {
  if (stock > 15) return 'success'
  if (stock > 5) return 'warning'
  return 'danger'
}

const goToBooks = () => {
  router.push('/books')
}

const loadStats = async () => {
  try {
    const [bookRes, categoryRes] = await Promise.all([
      bookApi.getAll(),
      categoryApi.getAll()
    ])

    const books = bookRes.data || []
    stats.value.bookCount = books.length
    stats.value.categoryCount = categoryRes.data?.length || 0
    stats.value.lowStockCount = books.filter(book => book.stock <= 5).length
    recentBooks.value = books.slice(0, 5)

    // 只有管理员才能获取用户数量和统计数据
    if (isAdmin.value) {
      try {
        const userRes = await userApi.getAll()
        stats.value.userCount = userRes.data?.length || 0

        // 加载统计数据
        const comprehensiveRes = await statisticsApi.getComprehensive()
        const comprehensiveData = comprehensiveRes.data || {}

        // 月度借阅趋势
        monthlyTrendData.value = comprehensiveData.monthlyTrend || []

        // 分类借阅统计
        categoryStatsData.value = comprehensiveData.categoryStats || []

        console.log('月度趋势数据:', monthlyTrendData.value)
        console.log('分类统计数据:', categoryStatsData.value)

      } catch (error) {
        console.error('加载管理员数据失败:', error)
        stats.value.userCount = 0
        monthlyTrendData.value = []
        categoryStatsData.value = []
      }
    }
  } catch (error) {
    console.error('加载基础数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>